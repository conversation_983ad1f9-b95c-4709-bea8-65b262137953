#ifndef BRIDGECONTROLLER_H
#define BRIDGECONTROLLER_H

#include "atcommand.h"
#include "atresponse.h"

class SerialManager;
class MqttClientManager;

/**
 * @brief 桥接控制器类
 *
 * 负责协调串口管理器和MQTT管理器，实现AT指令与MQTT操作的桥接
 */
class BridgeController : public QObject
{
    Q_OBJECT

public:
    explicit BridgeController(QObject *parent = nullptr);
    ~BridgeController();

    /**
     * @brief 启动桥接服务
     * @return 启动成功返回true
     */
    bool start();

    /**
     * @brief 停止桥接服务
     */
    void stop();

signals:

private slots:
    // === AT指令槽函数 ===

    /**
     * @brief 处理AT指令接收
     * @param command AT指令
     */
    void onAtCommandReceived(const AtCommand& command);

    /**
     * @brief 处理AT响应接收
     * @param response AT响应
     */
    void onAtResponseReceived(const AtResponse& response);

    /**
     * @brief 处理URC接收
     * @param response URC响应
     */
    void onUrcReceived(const AtResponse& response);

    // === MQTT URC事件槽函数 ===

    // MQTT事件处理
    /**
     * @brief 处理网络打开结果
     * @param clientIdx 客户端索引
     * @param result 结果码(0=成功)
     */
    void onNetworkOpened(int clientIdx, int result);
    
    /**
     * @brief 处理MQTT连接结果
     * @param clientIdx 客户端索引
     * @param result 结果码(0=成功)
     * @param retCode MQTT返回码
     */
    void onMqttConnected(int clientIdx, int result, int retCode);

    /**
     * @brief 处理MQTT断开结果
     * @param clientIdx 客户端索引
     * @param result 结果码(0=成功)
     */
    void onMqttDisconnected(int clientIdx, int result);

    /**
     * @brief 处理网络关闭结果
     * @param clientIdx 客户端索引
     * @param result 结果码(0=成功)
     */
    void onNetworkClosed(int clientIdx, int result);

    /**
     * @brief 处理MQTT状态变化
     * @param clientIdx 客户端索引
     * @param errorCode 错误码
     */
    void onMqttStatusChanged(int clientIdx, int errorCode);

    /**
     * @brief 处理MQTT消息接收
     * @param topic 主题
     * @param payload 负载
     */
    void onMqttMessageReceived(const QString& topic, const QByteArray& payload);

    /**
     * @brief 处理MQTT消息发布成功
     * @param topic 主题
     * @param messageId 消息ID
     */
    void onMqttMessagePublished(const QString& topic, int messageId);

    /**
     * @brief 处理MQTT订阅成功
     * @param topic 主题
     */
    void onMqttSubscribed(const QString& topic);

    /**
     * @brief 处理MQTT取消订阅成功
     * @param topic 主题
     */
    void onMqttUnsubscribed(const QString& topic);

private:
    /**
     * @brief 初始化桥接控制器
     * @return 初始化成功返回true
     */
    bool initialize();

    /**
     * @brief 处理AT指令
     * @param command AT指令
     */
    void processAtCommand(const AtCommand& command);

    /**
     * @brief 发送AT响应
     * @param response AT响应
     */
    void sendAtResponse(const AtResponse& response);

    /**
     * @brief 发送URC
     * @param urcType URC类型
     * @param parameters 参数列表
     */
    void sendUrc(UrcType urcType, const QStringList& parameters = QStringList());

    // AT指令处理方法
    bool processAt(const AtCommand& command);
    bool processQmtOpen(const AtCommand& command);
    bool processQmtClose(const AtCommand& command);
    bool processQmtConn(const AtCommand& command);
    bool processQmtDisc(const AtCommand& command);
    bool processQmtSub(const AtCommand& command);
    bool processQmtUnsub(const AtCommand& command);
    bool processQmtPub(const AtCommand& command);
    bool processQmtPubEx(const AtCommand& command);
    bool processQmtCfg(const AtCommand& command);

private:
    SerialManager* m_serialManager;             // 串口管理器
    MqttClientManager* m_mqttClientManager;         // MQTT客户端管理器
};

#endif // BRIDGECONTROLLER_H
