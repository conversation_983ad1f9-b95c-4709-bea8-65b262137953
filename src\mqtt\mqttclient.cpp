#include "mqttclient.h"
#include "log.h"

MqttClient::MqttClient(int clientIdx, QObject *parent)
    : QObject(parent)
    , m_clientIdx(clientIdx)
    , m_port(0)
    , m_state(IDLE)
    , m_mqttClient(nullptr)
{
    logTrace(QString("Creating MQTT client with index: %1").arg(m_clientIdx));

    // Initialize MQTT client
    m_mqttClient = new QMqttClient(this);

    // Connect MQTT client signals
    connect(m_mqttClient, &QMqttClient::connected,
            this, &MqttClient::onMqttConnected);
    connect(m_mqttClient, &QMqttClient::disconnected,
            this, &MqttClient::onMqttDisconnected);
    connect(m_mqttClient, &QMqttClient::errorChanged,
            this, &MqttClient::onMqttError);
    connect(m_mqttClient, &QMqttClient::messageReceived,
            this, &MqttClient::onMessageReceived);

    logTrace(QString("MQTT client %1 created").arg(m_clientIdx));
}

MqttClient::~MqttClient()
{
    logTrace(QString("Destroying MQTT client %1").arg(m_clientIdx));

    if (m_mqttClient && m_mqttClient->state() == QMqttClient::Connected) {
        logInfo(QString("Disconnecting MQTT client %1 during destruction").arg(m_clientIdx));
        m_mqttClient->disconnectFromHost();
    }
}

bool MqttClient::openNetwork(const QString& hostname, int port)
{
    logInfo(QString("AT+QMTOPEN: Client %1 opening network to %2:%3").arg(m_clientIdx).arg(hostname).arg(port));

    // Validate parameters
    if (hostname.isEmpty() || port <= 0 || port > 65535) {
        logError(QString("Invalid parameters for openNetwork: %1 %2").arg(hostname).arg(port));
        return false;
    }

    // Check current state
    if (m_state != IDLE) {
        logError(QString("Cannot open network in current state: %1").arg(m_state));
        return false;
    }

    // Store connection parameters
    m_hostname = hostname;
    m_port = port;

    // Update state
    setState(NET_READY);

    // Emit async URC
    QTimer::singleShot(10, [this](){
        emit networkOpened(m_clientIdx, 0); // 0 = success
    });


    logInfo(QString("Network configuration saved for client %1").arg(m_clientIdx));
    return true;
}

bool MqttClient::connectMqtt(const QString& clientId, const QString& username, const QString& password)
{
    logInfo(QString("AT+QMTCONN: Client %1 connecting with clientId: %2,username: %3, password: %4")
                .arg(m_clientIdx).arg(clientId, username, password));

    // Validate parameters
    if (clientId.isEmpty()) {
        logError("Client ID cannot be empty");
        return false;
    }

    // Check current state
    if (m_state != NET_READY) {
        logError(QString("Cannot connect MQTT in current state: %1 - network must be opened first").arg(m_state));
        return false;
    }

    // Store MQTT parameters
    m_clientId = clientId;
    m_username = username;
    m_password = password;

    // Update state
    setState(CONNECTING);

    // Configure MQTT client
    m_mqttClient->setHostname(m_hostname);
    m_mqttClient->setPort(static_cast<quint16>(m_port));
    m_mqttClient->setClientId(m_clientId);

    if (!m_username.isEmpty()) {
        m_mqttClient->setUsername(m_username);
    }
    if (!m_password.isEmpty()) {
        m_mqttClient->setPassword(m_password);
    }

    // Initiate connection (this combines TCP + MQTT connection)
    m_mqttClient->connectToHost();

    return true;
}

bool MqttClient::subscribe(int msgId, const QString& topic, int qos)
{
    logInfo(QString("AT+QMTSUB: Client %1 subscribing to topic: %2,QoS: %3,msgId: %4")
                .arg(m_clientIdx).arg(topic).arg(qos).arg(msgId));

    // Validate parameters
    if (topic.isEmpty() || (qos < 0) || (qos > 2) || (msgId < 1) || (msgId > 65535)) {
        logError("Invalid subscription parameters");
        return false;
    }

    // Check current state
    if (m_state != CONNECTED) {
        logError(QString("Cannot subscribe in current state: %1").arg(m_state));
        return false;
    }

    // Perform subscription
    auto subscription = m_mqttClient->subscribe(topic, static_cast<quint8>(qos));
    if (!subscription) {
        logError(QString("Failed to create subscription for topic: %1").arg(topic));
        emit subscribed(m_clientIdx, msgId, 2, 0); // 2 = failure
        return true; // Command was accepted, but operation failed
    }

    // Store subscription info
    m_subscriptions[topic] = qos;

    // Connect to subscription state change
    connect(subscription, &QMqttSubscription::stateChanged,
            [this, msgId, topic, qos](QMqttSubscription::SubscriptionState state) {
        if (state == QMqttSubscription::Subscribed) {
            logInfo(QString("Successfully subscribed to topic: %1").arg(topic));
            emit subscribed(m_clientIdx, msgId, 0, qos); // 0 = success
        } else if (state == QMqttSubscription::Error) {
            logError(QString("Subscription failed for topic: %1").arg(topic));
            m_subscriptions.remove(topic);
            emit subscribed(m_clientIdx, msgId, 2, 0); // 2 = failure
        }
    });

    return true;
}

bool MqttClient::publish(int msgId, int qos, bool retain, const QString& topic, const QByteArray& payload)
{
    logInfo(QString("AT+QMTPUBEX: Client %1 publishing to topic: %2,QoS: %3,retain: %4,msgId: %5,payload size: %6")
                .arg(m_clientIdx).arg(topic).arg(qos).arg(retain).arg(msgId).arg(payload.size()));

    // Validate parameters
    if (topic.isEmpty() || (qos < 0) || (qos > 2) || (msgId < 1) || (msgId > 65535)) {
        logError("Invalid publish parameters");
        return false;
    }

    // Check current state
    if (m_state != CONNECTED) {
        logError(QString("Cannot publish in current state: %1").arg(m_state));
        return false;
    }

    // Perform publish
    qint32 publishId = m_mqttClient->publish(topic, payload, static_cast<quint8>(qos), retain);
    if (publishId == -1) {
        logError(QString("Failed to publish message to topic: %1").arg(topic));
        emit published(m_clientIdx, msgId, 2); // 2 = failure
        return true; // Command was accepted, but operation failed
    }

    logInfo(QString("Message published successfully, publishId: %1").arg(publishId));

    // For QoS 0, emit success immediately
    // For QoS 1/2, we should wait for PUBACK/PUBCOMP, but QMqttClient doesn't provide easy access
    // So we emit success immediately for simplicity
    QTimer::singleShot(10, [this, msgId]() {
        emit published(m_clientIdx, msgId, 0); // 0 = success
    });

    return true;
}

bool MqttClient::disconnect()
{
    logInfo(QString("AT+QMTDISC: Client %1 disconnecting").arg(m_clientIdx));

    // Check current state
    if (m_state != CONNECTED) {
        logError(QString("Cannot disconnect in current state: %1").arg(m_state));
        return false;
    }

    // Update state
    setState(DISCONNECTING);

    // Disconnect from MQTT broker
    m_mqttClient->disconnectFromHost();

    logInfo(QString("Disconnect initiated for client %1").arg(m_clientIdx));
    return true;
}

bool MqttClient::closeNetwork()
{
    logInfo(QString("AT+QMTCLOSE: Client %1 closing network").arg(m_clientIdx));

    // Check current state - can close from any state except IDLE
    if (m_state == IDLE) {
        logError(QString("Network is not open for client %1").arg(m_clientIdx));
        return false;
    }

    // If still connected, disconnect first
    if (m_state == CONNECTED) {
        m_mqttClient->disconnectFromHost();
    }

    // Clear connection information
    m_hostname.clear();
    m_port = 0;
    m_clientId.clear();
    m_username.clear();
    m_password.clear();
    m_subscriptions.clear();

    // Update state
    setState(IDLE);

    // Emit async URC
    emit networkClosed(m_clientIdx, 0); // 0 = success

    logInfo(QString("Network closed for client %1").arg(m_clientIdx));
    return true;
}

// === Status Query Interface ===

bool MqttClient::isNetworkOpen() const
{
    return m_state >= NET_READY;
}

int MqttClient::getConnectionState() const
{
    switch (m_state) {
        case IDLE:
            return 1; // MQTT initialized
        case NET_READY:
            return 1; // MQTT initialized
        case CONNECTING:
            return 2; // MQTT connecting
        case CONNECTED:
            return 3; // MQTT connected
        case DISCONNECTING:
            return 4; // MQTT disconnecting
        default:
            return 1;
    }
}

QString MqttClient::getNetworkInfo() const
{
    if (m_state >= NET_READY) {
        return QString("+QMTOPEN: %1,\"%2\",%3")
               .arg(m_clientIdx)
               .arg(m_hostname)
               .arg(m_port);
    }
    return QString();
}

// === QMqttClient Event Handlers ===

void MqttClient::onMqttConnected()
{
    logInfo(QString("MQTT client %1 connected successfully").arg(m_clientIdx));

    // Update state
    setState(CONNECTED);

    // Emit connection success URC
    emitMqttConnectedAsync(0, 0); // result=0 (success), retCode=0 (accepted)
}

void MqttClient::onMqttDisconnected()
{
    logInfo(QString("MQTT client %1 disconnected").arg(m_clientIdx));

    if (m_state == DISCONNECTING) {
        // Normal disconnection
        setState(NET_READY);
        emit mqttDisconnected(m_clientIdx, 0); // 0 = success
    } else {
        // Unexpected disconnection
        logError(QString("Unexpected disconnection for client %1").arg(m_clientIdx));
        setState(NET_READY);
        //emit statusChanged(m_clientIdx, 1); // 1 = connection reset by server
    }
}

void MqttClient::onMqttError(QMqttClient::ClientError error)
{
    logError(QString("MQTT client %1 error: %2").arg(m_clientIdx).arg(error));

    if (m_state == CONNECTING) {
        // Connection failed
        setState(NET_READY);

        int result = 0;
        switch (error) {
        case QMqttClient::TransportInvalid:
            result = 2; //
            break;
        default:
            result = 0;
            break;
        }

        emitMqttConnectedAsync(result, static_cast<int>(error));
    } else {
        // Runtime error
        int errorCode = 1; // Default to connection reset
        emit statusChanged(m_clientIdx, errorCode);
    }
}

void MqttClient::onMessageReceived(const QByteArray& message, const QMqttTopicName& topic)
{
    logInfo(QString("MQTT client %1 received message on topic: %2,payload size: %3")
                .arg(m_clientIdx).arg(topic.name()).arg(message.size()));

    // Generate a message ID for the received message
    // In real implementation, this would come from the MQTT packet
    static int receivedMsgId = 1;

    emit messageReceived(m_clientIdx, receivedMsgId++, topic.name(), message);
}

// === Internal Helper Functions ===

void MqttClient::setState(State newState)
{
    if (m_state != newState) {
        logInfo(QString("Client %1 state changed from %2 to %3").arg(m_clientIdx).arg(m_state).arg(newState));
        m_state = newState;
    }
}

void MqttClient::emitMqttConnectedAsync(int result, int retCode)
{
    emit mqttConnected(m_clientIdx, result, retCode);
}
